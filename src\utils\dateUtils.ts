/**
 * Format date to Japanese format (YYYY年MM月DD日)
 * @param value - Date string in YYYY-MM-DD format
 * @returns Formatted Japanese date string
 */
export const formatDateJapan = (value: string | null | undefined): string => {
    if (value && typeof value === 'string' && value.length > 0) {
        const dates = value.split('-');
        if (dates.length >= 3) {
            const result = dates[0] + '年' + dates[1] + '月' + (dates[2].length > 2 ? dates[2].substring(0, 2) + '日' : `${dates[2]}日`);
            return result;
        }
    }
    return '';
};

/**
 * Format date to YYYY-MM-DD format
 * @param date - Date object
 * @returns Formatted date string
 */
export const formatDate = (date: Date): string => {
    const day = date.getDate().toString().length < 2 ? ('0' + date.getDate()) : date.getDate();
    const month = (date.getMonth() + 1).toString().length < 2 ? ('0' + (date.getMonth() + 1)) : (date.getMonth() + 1);
    const year = date.getFullYear();
    const result = year + "-" + month + "-" + day;
    return result;
};

/**
 * Format date for PDF export with timestamp
 * @param date - Date object
 * @returns Formatted date string for PDF
 */
export const formatDatePDF = (date: Date): string => {
    const day = date.getDate().toString().length < 2 ? ('0' + date.getDate()) : date.getDate();
    const month = (date.getMonth() + 1).toString().length < 2 ? ('0' + (date.getMonth() + 1)) : (date.getMonth() + 1);
    const year = date.getFullYear();
    const result = year + "-" + month + "-" + day + " " + date.getHours() + ":" + date.getMinutes() + ":" + date.getSeconds();
    return result;
};

/**
 * Format number with thousand separators
 * @param param - Number to format
 * @returns Formatted number string
 */
export const formatNumber = (param: number | undefined | null): string => {
    if (param === undefined || param === null) return '0';
    const result = new Intl.NumberFormat().format(param);
    return result;
};

/**
 * Format money with yen symbol
 * @param amount - Amount to format
 * @returns Formatted money string with yen symbol
 */
export const formatMoney = (amount: number): string => {
    return `¥ ${amount?.toLocaleString()}`;
};

/**
 * Format date to Japanese year-month format (YYYY年MM月)
 * @param value - Date string in YYYY-MM-DD format
 * @returns Formatted Japanese year-month string
 */
export const formatDateYearMonth = (value: string): string => {
    if (value && value.length > 0) {
        const dates = value.split("-");
        const result = dates[0] + "年" + dates[1] + "月";
        return result;
    } else {
        return "";
    }
};

export class DateUtils {
  /**
   * Format a date string (or Date object) to custom format.
   * Supported tokens: YYYY, MM, DD, HH, mm, ss
   */
  static format(dateInput: string | Date, formatStr: string): string {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;

    if (isNaN(date.getTime())) {
      throw new Error(`Invalid date: ${dateInput}`);
    }

    const pad = (n: number) => n.toString().padStart(2, '0');

    const map: Record<string, string> = {
      YYYY: date.getFullYear().toString(),
      MM: pad(date.getMonth() + 1),
      DD: pad(date.getDate()),
      HH: pad(date.getHours()),
      mm: pad(date.getMinutes()),
      ss: pad(date.getSeconds()),
    };

    return formatStr.replace(/YYYY|MM|DD|HH|mm|ss/g, (token) => map[token]);
  }

  /**
   * Parse a string to Date with a known format (e.g. "23-07-2025", "2025/07/23 14:30")
   * (Simple version — assumes valid and consistent format)
   */
  static parse(dateStr: string, formatStr: string): Date {
    const formatParts = formatStr.split(/[^A-Za-z]/);
    const dateParts = dateStr.split(/[^0-9]/);

    const values: Record<string, number> = {
      YYYY: 1970,
      MM: 1,
      DD: 1,
      HH: 0,
      mm: 0,
      ss: 0,
    };

    formatParts.forEach((part, i) => {
      if (Object.prototype.hasOwnProperty.call(values, part)) {
        values[part] = parseInt(dateParts[i], 10);
      }
    });

    return new Date(
      values['YYYY'],
      values['MM'] - 1,
      values['DD'],
      values['HH'],
      values['mm'],
      values['ss']
    );
  }

  /**
   * Get current date formatted
   */
  static nowFormatted(formatStr: string = 'YYYY-MM-DD'): string {
    return this.format(new Date(), formatStr);
  }
}

/**
 * Lấy khoảng ngày của tháng trước dựa vào ngày transferDate
 * Nếu transferDate là ngày 1-15 => trả về 1-15 tháng trước
 * Nếu transferDate là ngày 16-cuối tháng => trả về 16-cuối tháng trước
 * @param transferDate - string dạng YYYY-MM-DD
 * @returns { start: string, end: string } - dạng YYYY-MM-DD
 */
export function getPreviousMonthRangeByTransferDate(transferDate: string): { start: string, end: string } {
  if (!transferDate) return { start: '', end: '' };
  const date = new Date(transferDate);
  // Lùi về tháng trước
  const prevMonth = date.getMonth() === 0 ? 11 : date.getMonth() - 1;
  const prevYear = date.getMonth() === 0 ? date.getFullYear() - 1 : date.getFullYear();
  const day = date.getDate();
  // Lấy số ngày cuối tháng trước
  const lastDayPrevMonth = new Date(prevYear, prevMonth + 1, 0).getDate();
  let startDay = 1;
  let endDay = 15;
  if (day >= 16) {
    startDay = 16;
    endDay = lastDayPrevMonth;
  }
  const pad = (n: number) => n.toString().padStart(2, '0');
  const start = `${prevYear}-${pad(prevMonth + 1)}-${pad(startDay)}`;
  const end = `${prevYear}-${pad(prevMonth + 1)}-${pad(endDay)}`;
  return { start, end };
}

