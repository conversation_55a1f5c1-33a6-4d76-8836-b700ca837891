import { useAuthStore } from "@/features/auth/slices/authStore";
import { AccountTypes } from "@/types/globalType";

interface Notification {
  url: string,
  title: string,
  isActive: boolean
}

export const useGetNotification = () => {
  const { user } = useAuthStore();

  const getUrlConfig = () => {
    const notification: Notification = {
      url: "",
      title: "",
      isActive: false
    }

    if (user?.statusAccount === AccountTypes.APPLICATION_COMPLETE) {
      notification.url = "/store/config";
      notification.title = "申し込みを受け付けました。現在のステータスは加盟店情報よりご確認ください。";
      notification.isActive = true;
    }

    return notification;
  }


  return {
    getUrlConfig
  }
}