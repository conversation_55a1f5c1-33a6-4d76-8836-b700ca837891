import exclamationPointIcon from "@/assets/images/グループ 1351.svg";
import flagIcon from "@/assets/images/グループ 1350.svg";
import { Link } from "react-router-dom";
import { useGetUrlRedirect, useGetNotification } from "@/features/overview";
import { TypeStore } from "@/types/globalType";

const Notification = () => {
  const { getUrlConfig } = useGetNotification();
  const { title, url, isActive } = getUrlConfig();

  return (
    <>
      <div className="flex">
        <img src={exclamationPointIcon} alt="" />
        <h2 className="text-xl md:text-2xl text-[#6F6F6E]">お知らせ</h2>
      </div>
      <div className="min-h-[130px] mt-4 w-full max-w-full xl:max-w-[90%] lg:max-w-[1373px] border border-gray-500 rounded-lg bg-white shadow-md">
        <div className="p-4 md:p-4 lg:pl-10">
          <div className="flex items-start">
            <div className="">
              <ul className="py-0 text-[#6F6F6E] list-disc pl-5">
                { isActive && (
                  <li className=" text-[#FF0002] hover:text-[#FF0002]/80 py-1">
                    <div className="flex items-center space-x-2 font-medium">
                      <Link 
                        to={url} 
                        className="text-[18px] md:text-[20px] lg:text-[22px] hover:underline cursor-pointer"
                      >
                        {title}
                      </Link>
                    </div>
                  </li>
                  )
                }
                
                <li>
                  <div className="flex items-center space-x-2 font-normal">
                    <span className="text-[18px] md:text-[20px] lg:text-[22px]">キャンペーンのご案内</span>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

const ShortcutsMenu = () => {
  const { shortcutItems, typeStore } = useGetUrlRedirect();

  const getClassShortcutGird = () => {
    if (typeStore === TypeStore.STORE_PAYGATE) {
      return "w-full md:w-[100%] lg:w-[90%] xl:w-[55%] grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-3 gap-6 xl:gap-7"
    }

    return "w-full md:w-[100%] lg:w-[90%] xl:w-[70%] grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-4 gap-6 xl:gap-7"
  }

  return (
    <>
      <div className="flex items-center py-6">
        <img src={flagIcon} alt="" />
        <h2 className="text-xl md:text-2xl text-[#6F6F6E]">
          ショートカット
        </h2>
      </div>

      {/* Shortcuts Grid */}
      <div className={getClassShortcutGird()}>
        { shortcutItems.map((item, index) => {
          if (item.isExternal) {
            return (
              <a key={index} href={item.url} className="shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white">
                <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                  <img src={item.icon} alt="" className={item.iconClass} />
                  <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">{item.title}</span>
                </div>
              </a>
            )
          }
          return (
            <Link 
              key={index}
              to={item.url}
              className="shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white"
            >
              <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                <img src={item.icon} alt="" className={item.iconClass} />
                <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">{item.title}</span>
              </div>
            </Link>
          )
          })
        }
      </div>
    </>
  )
}

const Overview = () => {
  const { merchantNo, storeName } = useGetUrlRedirect();

  return (
    <div className="min-h-[calc(100vh-180px)] p-4 md:py-6 md:px-1 xl:p-6 text-[20px]">
      <h2 className="text-[20px] md:text-[22px] lg:text-[24px] text-[#6F6F6E]">
        こんにちは、加盟店番号{merchantNo} {storeName}さん
      </h2>
      <div className="py-6 mt-2">
        <Notification />
        <ShortcutsMenu />
      </div>
    </div>
  );
}

export default Overview;
