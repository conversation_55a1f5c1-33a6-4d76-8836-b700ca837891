import * as React from "react";
import {
  Di<PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";

interface CreditCardNoticeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  onConfirm: () => void;
}

const CreditCardNoticeDialog: React.FC<CreditCardNoticeDialogProps> = ({
  open,
  onOpenChange,
  onCancel,
  onConfirm,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="
          p-3 xs:p-4 sm:p-6 
          rounded-[15px] xs:rounded-[20px] sm:rounded-[25px] 
          bg-white 
          w-[98vw] xs:w-[95vw] sm:w-[90vw] md:w-[750px] 
          max-w-[750px] 
          max-h-[98vh] xs:max-h-[95vh] sm:max-h-[90vh] 
          overflow-y-auto 
          top-[50%] sm:top-[44%]
          left-[50%]
          transform 
          translate-x-[-50%] 
          translate-y-[-50%]
        "
      >
        <div className="text-muted-foreground whitespace-pre-wrap text-left my-1 xs:my-2 sm:my-4">
          <p className="text-center mb-[2rem] xs:text-sm sm:text-base md:text-[20px]">
            クレジットカードでのお支払いについて
          </p>
          <div className="space-y-1 px-2 xs:px-8 sm:px-10 sm:space-y-0 xs:text-sm sm:text-base md:text-[20px]">
            <div className="mb-1 sm:mb-2 leading-relaxed">・クレジットカードで端末代金はお支払い出来ません</div>
            <div className="mb-1 leading-relaxed">・チョキからお振込する売上金から月額費用が相殺できない場合のみ</div>
            <div className="leading-relaxed pl-2 xs:pl-4">&nbsp;&nbsp;&nbsp;&nbsp;登録のクレジットカードにて月額費用を請求します</div>
          </div>

          <p className="text-center mt-6 xs:mt-6 sm:mt-[50px] mb-[2rem] xs:text-base md:text-[20px]">
            クレジットカード登録に関する注意事項
          </p>
          <div className="space-y-1 px-2 xs:px-8 sm:px-10 sm:space-y-0 xs:text-sm sm:text-base md:text-[20px]">
            <div className="mb-1 sm:mb-2 leading-relaxed">・GMOペイメントゲートウェイのページにて登録を行います</div>
            <div className="mb-1 text-[#C44546] leading-relaxed ">・「入力したカード情報を保存する場合はチェックしてください」に</div>
            <div className="mb-1 sm:mb-2 text-[#C44546] leading-relaxed pl-2 xs:pl-4">&nbsp;&nbsp;&nbsp;&nbsp;必ずチェックを入れてください</div>
            <div className="mb-1 text-[#C44546] leading-relaxed ">・カードの有効性を確認するために1円の決済を行いますが</div>
            <div className="text-[#C44546] leading-relaxed pl-2 xs:pl-4">&nbsp;&nbsp;&nbsp;&nbsp;ご請求は発生いたしません。</div>
          </div>
        </div>

        <DialogFooter className="
          flex 
          flex-col sm:flex-row 
          gap-2 sm:gap-3 
          !justify-center 
          items-center 
          w-full 
          mt-2 xs:mt-3 sm:mt-0
          px-1 xs:px-0
          pt-4
        ">
          <Button
            variant="secondary"
            onClick={onCancel}
            className="
              bg-gray-400 
              hover:bg-gray-500 
              text-white 
              rounded-xl 
              text-xs xs:text-sm sm:text-[16px] 
              font-[400] 
              w-full sm:w-[122px] 
              h-[36px] xs:h-[40px] sm:h-[42px]
              order-2 sm:order-1
              min-h-[36px]
            "
          >
            戻る
          </Button>
          <Button
            onClick={onConfirm}
            className="
              bg-[#c94e4e] 
              hover:bg-[#c93838] 
              text-white 
              rounded-xl 
              text-xs xs:text-sm sm:text-[16px] 
              font-[400] 
              w-full sm:w-[122px] 
              h-[36px] xs:h-[40px] sm:h-[42px]
              order-1 sm:order-2
              min-h-[36px]
            "
          >
            登録へ進む
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreditCardNoticeDialog;