import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info } from "lucide-react";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { Section, Column } from "@/features/store/invoice-receipt/components/Section";
import { useGetDataInvoiceReceipt } from "../../../features/adminStore/invoice-receipt/hooks/useGetDataInvoiceReceipt";
import { useGmoServices } from "../../../features/adminStore/invoice-receipt/hooks/useGetInforGmo";
import { AccountTypes } from "@/types/globalType";
import CreditCardNoticeDialog from "@/features/store/invoice-receipt/components/CreditCardNoticeDialog";

// Định nghĩa cột cho bảng terminal fees
const terminalColumns: Column[] = [
  { key: 'yearMonth', label: '月額費用' },
  { key: 'total', label: '金額 (税込)' },
  { key: 'invoiceUrl', label: '請求書' },
  { key: 'receiptUrl', label: '領収書' },
];

const routePrefix = "/admin-store";

const InvoiceReceipt = () => {
  const { data, isLoading, error } = useGetDataInvoiceReceipt();
  const {
    gmoInfo,
    handlePaymentGMO,
    handleCreateLinkplusUrl,
    isLoadingPayment,
    isLoadingLinkplus,
    user,
    open,
    setOpen,
    handleUsageDetails
  } = useGmoServices();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="py-6 px-4 md:py-6 md:px-1 lg:px-6 lg:pt-6 lg:pb-1 text-[20px]">
      {/* Header */}
      <div className="space-y-6">
        <p className="text-[#707070] text-[24px]">月額費用</p>
      </div>

      <hr className="mt-5 mb-4 border-[#707070]" />

      {/* Error Alert */}
      {/* {error && (
        <Alert className="mb-4 border-red-200 bg-red-50">
          <Info className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">
            {error.message}
          </AlertDescription>
        </Alert>
      )} */}

      <CreditCardNoticeDialog
        open={open}
        onOpenChange={setOpen}
        onCancel={() => setOpen(false)}
        onConfirm={handleUsageDetails}
      />

      {
        (user && user?.statusAccount == AccountTypes.APPLICATION_COMPLETE) || (!gmoInfo?.memberId || !gmoInfo?.cardNo || !gmoInfo) && (
          <div className="w-full xl:w-[60%] text-center gap-2 text-base sm:text-lg md:text-xl lg:text-[24px] mb-3 bg-[#1D9987] text-white px-3 sm:px-4 py-2 mt-6 sm:mt-8 md:mt-10 rounded-sm cursor-pointer"
            onClick={() => setOpen(true)}
          >
            月額費用のクレジットカード払いか可能に!!
            <br />
            登録はこちら
          </div>
        )
      }

      {/* Main Content */}
      {/* Terminal Fees Section */}
      <div className="pt-8 pb-4">
        <Section
          columns={terminalColumns}
          data={data?.data || []}
          routePrefix={routePrefix}
          role="admin"
        />
      </div>

      {/* GMO Section */}
      {
        (gmoInfo && gmoInfo?.memberId && gmoInfo?.cardNo) && (
          <div className='mt-8'>
              {/* Header */}
              <div className='bg-gray-500 text-white px-4 py-3 text-[26px]'>
                  <h2 className='text-lg font-medium'>チョキペイの月額費用等がクレジットカード支払いが可能に！</h2>
              </div>
              
              {/* Content */}
              <div className='py-4 space-y-3'>
                  <p className='text-[#6F6F6E] text-[16px] leading-relaxed font-medium'>
                      クレジットカード登録をすると、チョキペイの月額費用等の支払いがクレジットカードからの引き落としが可能になります。
                  </p>
                  <p className='text-[#6F6F6E] text-[16px] leading-relaxed font-medium'>
                      （クレジットカード登録は、GMOペイメントゲートウェイのページで行います）
                  </p>
                  <p className='text-[#C44546] text-[16px] leading-relaxed font-medium'>
                      ※ 「入力したクレジットカード情報を保存する場合はチェックしてください。」に必ずチェックを入れてください。
                  </p>
                  <p className='text-[#C44546] text-[16px] leading-relaxed font-medium'>
                      ※ カードの有効性を確認するために1円の決済を行いますが、ご請求は発生致しません。
                  </p>
                  
                  {gmoInfo && (
                    <div className='space-y-2 mt-4'>
                      <p className='text-[#6F6F6E] text-[16px] font-medium'>会員ID: {gmoInfo.memberId}</p>
                      <p className='text-[#6F6F6E] text-[16px] font-medium'>登録カード番号: {gmoInfo.cardNo}</p>
                    </div>
                  )}
              </div>
              
              {/* Action Button */}
              <div className='bg-[#1D9987] hover:bg-[#1D9987] transition-colors cursor-pointer w-1/2 mx-auto rounded-md'
                  onClick={() => gmoInfo ? handleCreateLinkplusUrl() : handlePaymentGMO()}>
                  <div className='px-4 py-2 text-center'>
                      <h2 className='text-white font-medium flex items-center justify-center gap-2'>
                          {(isLoadingPayment || isLoadingLinkplus) ? (
                            <span>処理中...</span>
                          ) : (
                            <>
                              <span className='text-3xl'>▶︎</span>
                              <span>
                                  {gmoInfo ? 'クレジットカード編集はこちら' : 'クレジットカード登録はこちら'}
                              </span>
                            </>
                          )}
                      </h2>
                  </div>
              </div>
          </div>
        )}
    </div>
  );
};

export default InvoiceReceipt;