import React, { useMemo, useState } from 'react';
import { Link, useParams, useSearchParams } from 'react-router-dom';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useAdminDepositDetail } from '../hooks/useAdminDepositDetail';
import { AdminDepositDetailCSVData } from '../types';
import { formatDateJapan, formatNumber, getPreviousMonthRangeByTransferDate } from '@/utils/dateUtils';
import { mapTransactionType } from '@/constants/common.constant';
import { PDFTemplates } from '@/services/pdfTemplates';
import { CSVLink } from 'react-csv';
import _ from 'lodash';
import { useDynamicTableWidth } from '@/hooks/useDynamicTableWidth';
import { ArrowDownNarrowWide, ArrowDownWideNarrow, Download } from 'lucide-react';

interface AdminDepositDetailPageProps {
  agxMerchantNo: string;
}

type SortField = 'transactionDate' | 'transactionType' | 'salesAmount';
type SortDirection = 'asc' | 'desc';

export const AdminDepositDetailPage: React.FC<AdminDepositDetailPageProps> = ({ agxMerchantNo }) => {
  const { transferDate, transactionType, merchantNo, paymentBId } = useParams<{
    transferDate: string;
    transactionType?: string;
    merchantNo?: string;
    paymentBId?: string;
  }>();
  const [searchParams] = useSearchParams();
  const area = searchParams.get('area');
  const subArea = searchParams.get('subArea');
  const merchant = searchParams.get('merchant');
  const tableMaxWidth = useDynamicTableWidth();

  // Sorting state
  const [sortField, setSortField] = useState<SortField | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  // Search state
  const [searchUsageDate, setSearchUsageDate] = useState<string>('all');
  const [searchTransactionType, setSearchTransactionType] = useState<string>('all');

  // Temporary search state (for form inputs before search button is clicked)
  const [tempSearchUsageDate, setTempSearchUsageDate] = useState<string>('all');
  const [tempSearchTransactionType, setTempSearchTransactionType] = useState<string>('all');

  // Export hover state
  const [isExportHovered, setIsExportHovered] = useState(false);

  // Fetch data using React Query
  const { data, isLoading, error } = useAdminDepositDetail(
    agxMerchantNo,
    transferDate || '',
    transactionType,
    merchantNo || merchant,
    paymentBId,
    area,
    subArea
  );

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Filter and sort data
  const sortedData = useMemo(() => {
    if (!data?.data) {
      return [];
    }

    // First apply filters
    let filteredData = [...data.data];

    // Filter by usage date (利用日)
    if (searchUsageDate !== 'all') {
      filteredData = filteredData.filter(item =>
        item.transactionDate === searchUsageDate
      );
    }

    // Filter by transaction type (取引種別)
    if (searchTransactionType !== 'all') {
      const selectedTypeNumber = parseInt(searchTransactionType);
      filteredData = filteredData.filter(item =>
        item.transactionType === selectedTypeNumber
      );
    }

    // Then apply sorting if sortField is set
    if (!sortField) {
      return filteredData;
    }

    const sorted = filteredData.sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortField) {
        case 'transactionDate':
          aValue = a.transactionDate;
          bValue = b.transactionDate;
          break;
        case 'transactionType':
          aValue = `${mapTransactionType.get(a.transactionType)}${a.groupCodeName}`;
          bValue = `${mapTransactionType.get(b.transactionType)}${b.groupCodeName}`;
          break;
        case 'salesAmount':
          aValue = a.salesAmount;
          bValue = b.salesAmount;
          break;
        default:
          return 0;
      }

      if (sortField === 'salesAmount') {
        // Numeric comparison for sales amount
        const numA = Number(aValue);
        const numB = Number(bValue);
        return sortDirection === 'asc' ? numA - numB : numB - numA;
      } else {
        // String comparison for date and transaction type
        const strA = String(aValue);
        const strB = String(bValue);
        const comparison = strA.localeCompare(strB);
        return sortDirection === 'asc' ? comparison : -comparison;
      }
    });

    return sorted;
  }, [data?.data, sortField, sortDirection, searchUsageDate, searchTransactionType]);

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowDownNarrowWide className="w-5 h-5 text-[#1D9987]" />;
    }
    return sortDirection === 'asc'
      ? <ArrowDownNarrowWide className="w-5 h-5 text-[#1D9987]" />
      : <ArrowDownWideNarrow className="w-5 h-5 text-[#1D9987]" />;
  };

  // CSV Export data
  const csvHeaders = [
    { label: '利用日', key: 'transactionDate' },
    { label: '加盟店ID', key: 'merchantNo' },
    { label: '加盟店名', key: 'storeName' },
    { label: '取引種別', key: 'transactionType' },
    { label: '売上額', key: 'salesAmount' },
    { label: '振込日', key: 'paymentDate' },
    { label: '会員番号', key: 'memberId' }
  ];

  const csvData: AdminDepositDetailCSVData[] = useMemo(() => {
    if (!sortedData || sortedData.length === 0) return [];

    return sortedData.map(item => ({
      transactionDate: item.transactionDate,
      merchantNo: item.merchantNo,
      storeName: item.storeName,
      transactionType: `${mapTransactionType.get(item.transactionType)}${item.groupCodeName}`,
      salesAmount: item.salesAmount,
      paymentDate: item.paymentDate,
      memberId: item.memberId
    }));
  }, [sortedData]);

  // Calculate filtered totals
  const filteredTotals = useMemo(() => {
    return {
      count: sortedData.length,
      totalSales: sortedData.reduce((sum, item) => sum + item.salesAmount, 0)
    };
  }, [sortedData]);

  // Get unique merchant numbers for display
  const displayMerchantNos = useMemo(() => {
    if (!data?.data) return '';
    if (merchantNo) return merchantNo;

    const uniqueMerchants = _.uniqBy(data.data, 'merchantNo');
    return uniqueMerchants.map((item, index) =>
      index === 0 ? item.merchantNo : ` , ${item.merchantNo}`
    ).join('');
  }, [data, merchantNo]);

  // Get unique transaction types for filter dropdown
  const uniqueTransactionTypes = useMemo(() => {
    if (!data?.data) return [];

    const unique = _.uniqBy(data.data, 'transactionType');
    return unique.map(item => ({
      value: item.transactionType,
      label: `${mapTransactionType.get(item.transactionType)}${item.groupCodeName}`
    })).sort((a, b) => a.label.localeCompare(b.label));
  }, [data?.data]);

  // Get unique usage dates for filter dropdown
  const uniqueUsageDates = useMemo(() => {
    if (!data?.data) return [];

    const unique = _.uniq(data.data.map(item => item.transactionDate));
    return unique.sort((a, b) => b.localeCompare(a)); // Sort descending (newest first)
  }, [data?.data]);



  const handleExportPDF = async () => {
    if (!data) {
      alert('エクスポートするデータがありません。');
      return;
    }

    try {
      await PDFTemplates.generateAdminDepositDetailPDF({
        data,
        transferDate: transferDate || '',
        merchantNos: displayMerchantNos
      });
    } catch (error) {
      console.error('PDF export failed:', error);
      alert('PDF export failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mx-6 my-4">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!data) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-gray-600">データが見つかりません。</h2>
      </div>
    );
  }

  return (
    <div className="py-4 mb-16 text-lg text-[#6F6F6E]">
      {/* Header Section */}
      <div className="flex items-center py-4 gap-4 lg:gap-12 border-b border-[#6F6F6E] flex-wrap md:px-2 lg:px-6 px-4">
        {/* Filters and Export Section */}
        <div className='flex items-center lg:gap-10 gap-4 flex-wrap'>
          <div className='flex items-center lg:gap-8 gap-4 flex-wrap'>
            {/* Usage Date Search Field */}
            <div className='flex items-center md:gap-4 gap-2'>
              <Label className="text-2xl text-[#6F6F6E]">振込日</Label>
              <Select value={tempSearchUsageDate} onValueChange={setTempSearchUsageDate}>
                <div className="relative min-w-[200px] md:min-w-[260px] w-full">
                  <SelectTrigger className="text-[#6F6F6E] text-xl border-gray-300 border-2 shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                    <SelectValue />
                  </SelectTrigger>
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
                  </div>
                </div>
                <SelectContent>
                  <SelectItem value="all" className="text-[#6F6F6E] text-xl">全て</SelectItem>
                  {uniqueUsageDates.map((date) => (
                    <SelectItem key={date} value={date} className="text-[#6F6F6E] text-xl">
                      {date}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Transaction Type Search Field */}
            <div className='flex items-center md:gap-4 gap-2'>
              <Label className="text-2xl text-[#6F6F6E]">決済種別</Label>
              <Select value={tempSearchTransactionType} onValueChange={setTempSearchTransactionType}>
                <div className="relative min-w-[200px] md:min-w-[260px] w-full">
                  <SelectTrigger className="text-[#6F6F6E] text-xl border-gray-300 border-2 shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                    <SelectValue />
                  </SelectTrigger>
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
                  </div>
                </div>
                <SelectContent>
                  <SelectItem value="all" className="text-[#6F6F6E] text-xl">全て</SelectItem>
                  {uniqueTransactionTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value.toString()} className="text-[#6F6F6E] text-xl">
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          {/* Search Button */}
          <Button
            variant="outline"
            onClick={() => {
              setSearchUsageDate(tempSearchUsageDate);
              setSearchTransactionType(tempSearchTransactionType);
            }}
            className="text-xl px-6 bg-gray-100 hover:bg-[#1D9987]/80 hover:text-white hover:border-[#1D9987] border-black text-[#6F6F6E] shadow-md"
          >
            検索
          </Button>
        </div>
        {/* Export Buttons - Following ExportButtons.tsx design pattern */}
        <div className="relative">
          <div
            className="relative"
            onMouseEnter={() => setIsExportHovered(true)}
            onMouseLeave={() => setIsExportHovered(false)}
          >
            {/* Download Icon Button */}
            <button
              className="p-2 text-[#1D9987] hover:text-[#1D9987]/80 hover:opacity-80 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={!data || sortedData.length === 0}
            >
              <Download className="h-8 w-8" />
            </button>

            {/* Dropdown Menu */}
            {isExportHovered && data && sortedData.length > 0 && (
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-32 bg-white border border-gray-300 rounded shadow-md z-50">
                {/* @ts-expect-error : type mismatch due to version node */}
                <CSVLink
                  data={csvData}
                  headers={csvHeaders}
                  filename={`deposit-detail-${transferDate}.csv`}
                  enclosingCharacter=""
                  className="block w-full px-6 py-3 text-center text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 border-b border-gray-200 transition-colors duration-150 no-underline"
                >
                  CSV
                </CSVLink>
                <button
                  onClick={handleExportPDF}
                  className="w-full px-6 py-3 text-center text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150"
                >
                  PDF
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Usage Period Display */}
        <span className="text-2xl">
          利用期間｜{
            (() => {
              const { start, end } = getPreviousMonthRangeByTransferDate(tempSearchUsageDate === 'all' ? '' : tempSearchUsageDate);
              if (!start || !end) return '20xx年mm月dd日〜20xx年mm月dd日';
              return `${formatDateJapan(start)}〜${formatDateJapan(end)}`;
            })()
          }
        </span>
      </div>

      {/* Summary Information */}
      <div className="px-4 grid grid-cols-1 md:grid-cols-4 gap-2 md:px-2 lg:px-6 my-10 w-full text-2xl">
        <div>
          <span>加盟店ID: </span>
          <span>{displayMerchantNos}</span>
        </div>
        <div>
          <span>件数: </span>
          <span>{formatNumber(filteredTotals.count)}</span>
        </div>
        <div>
          <span>売上額の合計: </span>
          <span>{formatNumber(filteredTotals.totalSales)}</span>
        </div>
      </div>

      {/* Search Parameters Information */}
      <div className="px-4 grid grid-cols-1 md:grid-cols-4 gap-2 md:px-2 lg:px-6 my-10 w-full text-2xl">
        <div>
          <span>エリア: </span>
          <span>{area}</span>
        </div>
        <div>
          <span>サブエリア: </span>
          <span>{subArea}</span>
        </div>
        <div>
          <span>加盟店: </span>
          <span>{merchant}</span>
        </div>
      </div>

      {/* Detail Table */}
      <div className="overflow-x-auto mx-auto md:px-2" style={{ maxWidth: tableMaxWidth }}>
        <Table className="min-w-[1200px]">
          <TableHeader>
            <TableRow className="border-none">
              <TableHead className="text-center bg-white text-xl">
                <div className="w-[80%] inline-flex items-center justify-center gap-2 border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">
                  利用日
                  <button
                    onClick={() => handleSort('transactionDate')}
                    className="rounded transition-colors"
                  >
                    {renderSortIcon('transactionDate')}
                  </button>
                </div>
              </TableHead>
              <TableHead className="text-center bg-white text-xl hidden">
                <span className="w-[80%] inline-block border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">加盟店ID</span>
              </TableHead>
              <TableHead className="text-center bg-white text-xl hidden">
                <span className="w-[80%] inline-block border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">加盟店名</span>
              </TableHead>
              <TableHead className="text-center bg-white text-xl" colSpan={2}>
                <div className="w-[80%] inline-flex items-center justify-center gap-2 border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">
                  取引種別
                  <button
                    onClick={() => handleSort('transactionType')}
                    className="rounded transition-colors"
                  >
                    {renderSortIcon('transactionType')}
                  </button>
                </div>
              </TableHead>
              <TableHead className="text-center bg-white text-xl">
                <div className="w-[80%] inline-flex items-center justify-center gap-2 border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">
                  売上額
                  <button
                    onClick={() => handleSort('salesAmount')}
                    className="rounded transition-colors"
                  >
                    {renderSortIcon('salesAmount')}
                  </button>
                </div>
              </TableHead>
              <TableHead className="text-center bg-white text-xl hidden">
                <span className="w-[80%] inline-block border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">振込日</span>
              </TableHead>
              <TableHead className="text-center bg-white text-xl">
                <span className="w-[80%] inline-block border-b border-[#6F6F6E] px-2 py-3 text-[#6F6F6E]">会員番号</span>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedData.map((item, index) => (
              <TableRow key={index} className="border-0">
                <TableCell className="text-center bg-white text-xl">
                  <span className="w-[80%] inline-block">{item.transactionDate}</span>
                </TableCell>
                <TableCell className="text-center bg-white text-xl hidden">
                  <span className="w-[80%] inline-block">{item.merchantNo}</span>
                </TableCell>
                <TableCell className="text-left bg-white text-xl hidden">
                  <span className="w-[80%] inline-block">{item.storeName}</span>
                </TableCell>
                <TableCell className="text-center bg-white text-xl" colSpan={2}>
                  <span className="w-[80%] inline-block text-left px-4">{`${mapTransactionType.get(item.transactionType)}${item.groupCodeName}`}</span>
                </TableCell>
                <TableCell className="text-center bg-white text-xl">
                  <span className="w-[80%] inline-block">{formatNumber(item.salesAmount)}円</span>
                </TableCell>
                <TableCell className="text-center bg-white text-xl hidden">
                  <span className="w-[80%] inline-block">{item.paymentDate}</span>
                </TableCell>
                <TableCell className="text-center bg-white text-xl">
                  <span className="w-[80%] inline-block">{item.memberId}</span>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
