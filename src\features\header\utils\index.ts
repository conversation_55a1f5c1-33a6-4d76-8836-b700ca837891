// Function to get menu name based on current pathname
export const getMenuNameFromPath = (pathname: string): string => {
  // Remove leading slash and get the first segment
  const segments = pathname.split('/').filter(Boolean);
  const firstSegment = segments[0] || '';
  const secondSegment = segments[1] || '';
  const thirdSegment = segments[2] || '';
  
  // Map paths to menu names
  switch (firstSegment) {
    case 'overview':
      return 'ホーム';
    case 'store':
      if (secondSegment === 'deposit') return '振込一覧';
      if (secondSegment === 'invoice-receipt') return '領収書・請求書';
      if (secondSegment === 'config') return '加盟店情報';
      if (secondSegment === 'change-store-information') return '加盟店情報変更';
      if (secondSegment === 'notification' && thirdSegment === 'calendar') return '振込カレンダー';
      if (secondSegment === 'notification' && thirdSegment === 'paygate') return '初期設定';
      if (secondSegment === 'notification' && thirdSegment === 'faq') return '利用に関するFAQ';
      if (secondSegment === 'notification' && thirdSegment === 'rollpaper') return 'ロール紙の購入';
      if (secondSegment === 'support') return '端末サポートサイト';
      if (secondSegment === 'agreement') return '加盟店規約';
      if (secondSegment === 'agreement' && thirdSegment === 'paygate') return '加盟店規約';
      if (secondSegment === 'agreement' && thirdSegment === 'crepico') return '加盟店規約';
      if (secondSegment === 'crepico-payment') return '決済データ';
      return 'ホーム';
    case 'admin-store':
      if (secondSegment === 'deposit') return '振込一覧';
      if (secondSegment === 'invoice-receipt') return '領収書・請求書';
      // if (secondSegment === 'invoice-receipt' && thirdSegment === '') return '領収書・請求書';
      // if (secondSegment === 'invoice-receipt' && thirdSegment === 'admin-invoice-monthly') return '管理月次請求書';
      // if (secondSegment === 'invoice-receipt' && thirdSegment === 'admin-receipt-monthly') return '管理月次領収書';
      if (secondSegment === 'config') return 'エリア・店舗登録';
      if (secondSegment === 'notification' && thirdSegment === 'calendar') return '振込カレンダー';
      if (secondSegment === 'notification' && thirdSegment === 'paygate') return '初期設定';
      if (secondSegment === 'notification' && thirdSegment === 'faq') return '利用に関するFAQ';
      if (secondSegment === 'crepico-payment') return '決済データ';
      return 'ホーム';
    case 'payment-terminal':
      if (secondSegment === 'settings') return '端末設定';
      if (secondSegment === 'management') return '端末管理';
      return '決済端末';
    case 'help':
      if (secondSegment === 'guide') return '使い方ガイド';
      if (secondSegment === 'faq') return 'FAQ';
      if (secondSegment === 'contact') return 'お問い合わせ';
      return 'ヘルプ';
    case 'account':
      return 'アカウント設定';
    default:
      return 'ホーム';
  }
};