import { useStoreInvoice } from "@/features/store/invoice-receipt/hooks/useStoreInvoice";
import { Section, Column } from "@/features/store/invoice-receipt/components/Section";
import { InvoiceLayout } from "@/features/store/invoice-receipt/components/InvoiceReceiptLayout";
import { InvoiceStatus } from "@/features/store/invoice-receipt/types";
import { formatNumber } from "@/utils/dateUtils";
import { useAuthStore } from "@/features/auth/slices/authStore";

// Định nghĩa cột cho bảng terminal fees
const terminalColumns: Column[] = [
  { key: 'terminal', label: '端末代金' },
  { key: 'taxIncludedAmount', label: '税込金額' },
  { key: 'invoice', label: '請求書' },
  { key: 'receipt', label: '領収書' },
  { key: 'deadlineDate', label: 'お支払期限' },
];

// Định nghĩa cột cho bảng monthly fees
const monthlyColumns: Column[] = [
  { key: 'month', label: '月額費用' },
  { key: 'taxIncludedAmount', label: '税込金額' },
  { key: 'invoice', label: '請求書' },
  { key: 'receipt', label: '領収書' },
  { key: 'deadlineDate', label: 'お支払期限' },
];

const routePrefix = "/store/invoice-receipt/crepico"

const InvoiceReceiptCrepico = () => {
  const { user } = useAuthStore();

  const agxMerchantNoGetInvoice = user?.memberType ? (user?.agxMerchantNo || '') : (user?.agxNewMerchantNo || '');

  const {
    agxInvoice,
    agxInvoiceDetail,
    loading,
    error,
  } = useStoreInvoice(agxMerchantNoGetInvoice);

  // Transform agxInvoiceDetail thành format cho terminal table
  const terminalFeesData = agxInvoiceDetail ? [{
    terminal: "クレピコ AT-M100",
    taxIncludedAmount: formatNumber(agxInvoiceDetail.total),
    invoice: (agxInvoiceDetail.invoiceStatus === InvoiceStatus.PENDING || agxInvoiceDetail.invoiceStatus === InvoiceStatus.PAID) && agxInvoiceDetail.invoiceUrl,
    receipt: agxInvoiceDetail.invoiceStatus === InvoiceStatus.PAID && agxInvoiceDetail.receiptUrl,
    deadlineDate: agxInvoiceDetail.agxStatus === InvoiceStatus.PENDING ? agxInvoiceDetail.deadlineDate : '-'
  }] : [];

  // Transform agxInvoice thành format cho monthly table
  const monthlyFeesData = agxInvoice.map((item) => ({
    month: item.yearMonth,
    taxIncludedAmount: formatNumber(item.total),
    invoice: item.invoiceUrl,
    receipt: item.receiptUrl,
    deadlineDate: item.deadlineDate || '-'
  }));

  return (
    <InvoiceLayout
      title="端末代金と月額費用の請求書と領収書をご確認いただけます。"
      loading={loading}
      error={error}
    >
      {/* Terminal Fees Section */}
      <div className="pt-8 pb-4">
        <Section 
          columns={terminalColumns}
          data={terminalFeesData}
          routePrefix={routePrefix}
        />
      </div>

      {/* Monthly Fees Section */}
      <div className="py-4">
        <Section 
          columns={monthlyColumns}
          data={monthlyFeesData}
          routePrefix={routePrefix}
        />
      </div>
    </InvoiceLayout>
  );
};

export default InvoiceReceiptCrepico;
